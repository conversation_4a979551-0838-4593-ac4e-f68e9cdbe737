# Currency Switching Implementation Project Plan

## Project Overview

Implementation of multi-currency support for Evertrek website, coordinated with Rezkit's backend changes. The project enables USD pricing alongside existing GBP, with automatic currency detection and manual switching capabilities.

**Total Estimated Time: 12-14 hours** (within your 15-hour budget)

## Dependencies & Coordination Points

### External Dependencies (Rezkit)

1. **Pre-requisite updates to booking journey** ✅ (95% complete per Mark)
2. **Test SKU migration** (Rezkit - in progress)
3. **Live SKU migration** (Rezkit - coordinated with website update)
4. **Outstanding support tickets resolution** (Rezkit - file upload & Alpkit link)

### Critical Coordination Point

- **Live SKU migration must happen simultaneously with website URL structure update**
- Rezkit will coordinate timing for go-live

## Project Breakdown

### Phase 1: Preparation & Setup (3-4 hours)

**Must complete before <PERSON>zkit's live SKU migration**

#### Task 1.1: Enable USD Currency in CMS (0.5 hours)

- **Description**: Verify USD is enabled in `#__zencurrencies` table
- **Files**: Database configuration
- **Risk**: Low - likely already done
- **Deliverable**: USD currency active in system

#### Task 1.2: Implement IP-based Currency Detection (1.5 hours)

- **Description**: Enhance existing geolocation plugin to set currency based on user location
- **Files**:
    - `plugins/system/geolocate/geolocate.php`
    - `libraries/mrzen/helpers/ZenGeolocationHelper.php`
- **Current State**: Basic IP detection exists, needs currency mapping
- **Deliverable**: Automatic USD for US visitors, GBP for others


#### Task 1.3: Add Currency Switcher UI (1-2 hours)

- **Description**: Enhance existing currency switcher module for better visibility
- **Files**:
    - `modules/mod_zencurrencyswitcher/tmpl/default.php`
    - Template CSS files
- **Current State**: Basic switcher exists but may need styling improvements
- **Deliverable**: User-friendly currency switcher in header/footer

### Phase 2: Core URL Structure Changes (4-5 hours)

**Must be deployed simultaneously with Rezkit's live SKU migration**

#### Task 2.1: Update Book Now Button URLs (2-3 hours)

- **Description**: Change from `holiday:priceId` to `holiday:dateId-priceTypeId&currency=XXX`
- **Files**:
    - `templates/zenbase/html/com_zenholidays/holiday/default_dates-prices.php` (lines 301, 428)
- **Current Structure**: `?sku=holiday:<?= $date->prices[1]->id; ?>`
- **New Structure**: `?sku=holiday:<?= $date->id ?>-1&currency=<?= $userCurrency ?>`
- **Risk**: High - breaks booking if not coordinated with Rezkit
- **Deliverable**: Updated booking URLs using new format

#### Task 2.2: Add Currency Parameter to All Booking Links (1 hour)

- **Description**: Ensure all booking entry points include currency parameter
- **Files**: Search for all booking URL generation points
- **Deliverable**: Consistent currency parameter across all booking flows

#### Task 2.3: Update ZenProvider API for Multi-Currency (1 hour)

- **Description**: Verify `bookMultiCurrencyHoliday` method handles new URL format
- **Files**: `components/com_zenprovider/views/book/view.json.php`
- **Current State**: Method exists and looks correct
- **Deliverable**: Confirmed API compatibility

### Phase 3: Price Display & Validation (3-4 hours)

#### Task 3.1: Load and Input USD Prices (1 hour)

- **Description**: Coordinate with Evertrek team to load USD prices into Joomla
- **Files**: Admin interface or data import
- **Note**: Can be done in advance of go-live
- **Deliverable**: USD prices available in system

#### Task 3.2: Verify USD Price Display (1-2 hours)

- **Description**: Test price display in correct currency with proper formatting
- **Files**:
    - `templates/zenbase/html/com_zenholidays/holiday/default_dates-prices.php`
    - Currency formatting helpers
- **Test Cases**:
    - USD prices show with $ symbol
    - GBP prices show with £ symbol
    - Payment plans calculate correctly in both currencies
- **Deliverable**: Accurate price display in both currencies

#### Task 3.3: Test Currency Switching (1 hour)

- **Description**: End-to-end testing of currency switching functionality
- **Test Cases**:
    - IP-based detection works
    - Manual switching works
    - Session persistence works
    - Booking URLs generate correctly
- **Deliverable**: Fully functional currency switching

### Phase 4: Testing & Go-Live (2 hours)

#### Task 4.1: Pre-Go-Live Testing (1 hour)

- **Description**: Comprehensive testing before Rezkit coordination
- **Test Environment**: Development/staging
- **Test Cases**:
    - All booking URLs generate correctly
    - Currency detection works
    - Price display is accurate
    - No broken functionality
- **Deliverable**: Tested and ready code

#### Task 4.2: Coordinated Go-Live (1 hour)

- **Description**: Deploy changes in coordination with Rezkit's live SKU migration
- **Process**:
  1. Confirm Rezkit readiness
  2. Deploy URL structure changes
  3. Verify booking flow works
  4. Monitor for issues
- **Deliverable**: Live multi-currency system

## Risk Assessment

### High Risks

1. **Timing Coordination**: URL changes must be perfectly synchronized with Rezkit's SKU migration
   - **Mitigation**: Clear communication channel with Mark, staged deployment plan

2. **Booking Flow Breakage**: Incorrect URL format could break all bookings
   - **Mitigation**: Thorough testing, rollback plan, off-hours deployment

### Medium Risks

1. **Price Data Issues**: USD prices might not display correctly
   - **Mitigation**: Test with sample data first, validate formatting

2. **Session/Cookie Issues**: Currency switching might not persist
   - **Mitigation**: Test across different browsers and scenarios

### Low Risks

1. **IP Detection Accuracy**: Some users might get wrong currency initially
   - **Mitigation**: Manual switcher provides override option

## Unknowns & Questions

1. **USD Price Data**: When will USD prices be available for loading?
2. **Go-Live Timing**: Exact timing coordination with Rezkit
3. **Rollback Plan**: Process if issues arise during go-live
4. **Testing Access**: Access to Rezkit staging environment for testing

## Success Criteria

1. ✅ US visitors automatically see USD prices
2. ✅ UK/other visitors see GBP prices
3. ✅ Users can manually switch currencies
4. ✅ Currency selection persists across sessions
5. ✅ Booking URLs work correctly in both currencies
6. ✅ No disruption to existing booking flow
7. ✅ Price display formatting is correct for both currencies

## Workstream Summary

| Workstream | Duration | Dependencies | Risk Level |
|------------|----------|--------------|------------|
| **Preparation** | 3-4 hours | None - can start immediately | Low |
| **Core URL Changes** | 4-5 hours | Must complete after preparation | High |
| **Price Display** | 3-4 hours | Can run parallel to preparation | Medium |
| **Go-Live** | 2 hours | Requires Rezkit coordination | High |
| **Total Project** | **12-14 hours** | **5-day timeline** | **Medium** |

### Daily Time Commitment

- **Average**: 2.5 hours per day over 5 days
- **Peak**: 3 hours on Days 2-4
- **Minimum**: 1 hour on go-live day
- **Buffer**: Weekend available for issue resolution

## Next Steps

1. **Day 1 (Today)**: Enable USD in CMS + start IP detection (2h)
2. **Day 2**: Complete preparation tasks + load USD prices (3h)
3. **Day 3-4**: Core URL structure changes (6h total)
4. **Day 5**: Final testing and preparation (2h)
5. **Coordinate**: Establish go-live timing with Mark throughout week
6. **Go-Live**: Deploy in coordination with Rezkit (1h)

## Detailed Timeline & Workstreams

### Week 1: Development Phase (Days 1-5)

**Day 1 (Monday) - 2 hours**

- ✅ Enable USD in CMS (0.5h) - *Can be done immediately*
- 🔄 Start IP Currency Detection (1.5h) - *Begin implementation*

**Day 2 (Tuesday) - 3 hours**

- 🔄 Complete IP Currency Detection (remaining time)
- 🔄 Load USD Prices (1h) - *Coordinate with team*
- 🔄 Start Currency Switcher UI (1-2h)

**Day 3 (Wednesday) - 3 hours**

- 🔄 Complete Currency Switcher UI
- 🔄 Start Update Book Now URLs (2-3h) - *Critical task*

**Day 4 (Thursday) - 3 hours**

- 🔄 Complete Book Now URLs update
- 🔄 Add Currency Parameters (1h)
- 🔄 Verify Price Display (1-2h)

**Day 5 (Friday) - 2 hours**

- 🔄 Update ZenProvider API (1h)
- 🔄 Test Currency Switching (1h)
- 🔄 Pre-Go-Live Testing (1h)

**Weekend Buffer** - Address any issues found during testing

### Week 2: Go-Live Coordination

**Day 8 (Monday) - 1 hour**

- 🚨 **Coordinated Deployment** with Rezkit Live SKU Migration
- Real-time monitoring and issue resolution

### Project Gantt Chart

```mermaid
gantt
    title Currency Switching Implementation Timeline
    dateFormat YYYY-MM-DD
    axisFormat %m/%d

    section Phase 1 Preparation
    Enable USD in CMS (0.5h)           :done, usd, 2025-01-13, 1d
    IP Currency Detection (1.5h)       :active, ip, 2025-01-13, 2d
    Currency Switcher UI (1-2h)        :ui, after ip, 2d

    section Phase 2 Core Changes
    Update Book Now URLs (2-3h)        :crit, urls, after ui, 2d
    Add Currency Parameters (1h)       :params, after urls, 1d
    Update ZenProvider API (1h)        :api, after params, 1d

    section Phase 3 Price Display
    Load USD Prices (1h)              :prices, 2025-01-13, 2d
    Verify Price Display (1-2h)       :display, after ui, 2d
    Test Currency Switching (1h)      :test, after display, 1d

    section Phase 4 Go-Live
    Pre-Go-Live Testing (1h)          :testing, after api test, 1d
    Coordinated Deployment (1h)       :crit, deploy, 2025-01-20, 1d

    section Rezkit Dependencies
    Test SKU Migration                 :external, 2025-01-13, 5d
    Support Tickets Resolution         :external, 2025-01-13, 5d
    Live SKU Migration                 :crit, live, 2025-01-20, 1d
```

### Workstream Dependencies

```mermaid
graph TD
    A[Enable USD in CMS] --> B[IP Currency Detection]
    C[Load USD Prices] --> D[Verify Price Display]
    B --> E[Update Book Now URLs]
    E --> F[Add Currency Parameters]
    F --> G[Update ZenProvider API]
    D --> H[Test Currency Switching]
    G --> I[Pre-Go-Live Testing]
    H --> I
    I --> J[Coordinated Deployment]
    K[Rezkit Test Migration] --> J
    L[Rezkit Support Tickets] --> J
```

**Critical Path**: A → B → E → F → G → I → J (Total: 8-9 hours)
**Parallel Path**: C → D → H → I (Total: 3-4 hours)

### Resource Allocation

- **Jon (Developer)**: 12-14 hours over 5 days (2-3 hours per day)
- **Evertrek Team**: 1 hour (USD price loading coordination)
- **Rezkit Coordination**: 1 hour (go-live timing)
